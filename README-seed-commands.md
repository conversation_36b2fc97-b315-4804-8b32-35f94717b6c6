# 🌱 数据库种子数据管理指南

## 📋 概述

本项目提供了完整的数据库种子数据管理系统，支持不同环境（本地开发、Docker部署）和不同API实例（Kaia、Pharos）的数据初始化。

## 🎯 可用的 Seed 命令

### 📊 命令总览

| 环境 | API实例 | 命令 | 用途 |
|------|---------|------|------|
| 本地开发 | Kaia | `npm run local:seed:kaia` | 初始化Kaia本地开发数据库 |
| 本地开发 | Pharos | `npm run local:seed:pharos` | 初始化Pharos本地开发数据库 |
| Docker | Kaia | `npm run docker:seed:kaia` | 初始化Kaia Docker数据库 |
| Docker | Pharos | `npm run docker:seed:pharos` | 初始化Pharos Docker数据库 |

### 🔧 底层命令（高级用户）

| 命令 | 环境配置文件 | 用途 |
|------|-------------|------|
| `npm run seed:tasks:local:kaia` | `.env.local.kaia` | Kaia本地环境种子数据 |
| `npm run seed:tasks:local:pharos` | `.env.local.pharos` | Pharos本地环境种子数据 |
| `npm run seed:tasks:docker:kaia` | `.env_kaia` | Kaia Docker环境种子数据 |
| `npm run seed:tasks:docker:pharos` | `.env_pharos` | Pharos Docker环境种子数据 |

### 🎮 游戏配置初始化命令

| 命令 | 用途 | 说明 |
|------|------|------|
| `npm run init:game-configs:kaia` | 初始化Kaia农场配置 | 农场配置+配送线配置 |
| `npm run init:game-configs:pharos` | 初始化Pharos农场配置 | 农场配置+配送线配置 |
| `npm run init:game-configs:both` | 初始化两个数据库农场配置 | 同时初始化Kaia和Pharos |
| `npm run init:game-configs:force` | 强制重新初始化农场配置 | 清空现有数据后重新初始化 |

### 🎯 任务配置初始化命令

| 命令 | 用途 | 说明 |
|------|------|------|
| `npm run init:task-configs:kaia` | 初始化Kaia任务配置 | 101条任务配置数据 |
| `npm run init:task-configs:pharos` | 初始化Pharos任务配置 | 101条任务配置数据 |
| `npm run init:task-configs:both` | 初始化两个数据库任务配置 | 同时初始化Kaia和Pharos |
| `npm run init:task-configs:force` | 强制重新初始化任务配置 | 清空现有数据后重新初始化 |

### 🚀 完整配置初始化命令

| 命令 | 用途 | 说明 |
|------|------|------|
| `npm run init:all-configs:kaia` | 初始化Kaia所有配置 | 农场+配送线+任务配置 |
| `npm run init:all-configs:pharos` | 初始化Pharos所有配置 | 农场+配送线+任务配置 |
| `npm run init:all-configs:both` | 初始化两个数据库所有配置 | 完整的游戏配置初始化 |
| `npm run init:all-configs:force` | 强制重新初始化所有配置 | 清空现有数据后重新初始化 |
| `npm run init:all-configs:check` | 检查所有配置状态 | 验证配置数据完整性 |

## 🚀 使用方法

### 本地开发环境

#### 1. 初始化Kaia本地数据库
```bash
# 确保Docker基础服务已启动
npm run local:docker-up

# 同步数据库结构
npm run sync:db:local:kaia

# 初始化种子数据
npm run local:seed:kaia
```

#### 2. 初始化Pharos本地数据库
```bash
# 确保Docker基础服务已启动
npm run local:docker-up

# 同步数据库结构
npm run sync:db:local:pharos

# 初始化种子数据
npm run local:seed:pharos
```

#### 3. 初始化游戏配置数据
```bash
# 初始化所有游戏配置（推荐）
npm run init:all-configs:kaia
npm run init:all-configs:pharos

# 或者一次性初始化两个数据库
npm run init:all-configs:both

# 分别初始化不同类型的配置
npm run init:game-configs:kaia    # 只初始化农场和配送线配置
npm run init:task-configs:kaia    # 只初始化任务配置
```

#### 4. 一键初始化两个数据库
```bash
# 使用本地开发脚本一键初始化
npm run local:setup

# 或者手动分别初始化
npm run local:seed:kaia
npm run local:seed:pharos
```

### Docker部署环境

#### 1. Kaia Docker部署
```bash
# 部署Kaia服务（自动执行seed）
./deploy-kaia.sh

# 或者手动执行seed（如果容器已运行）
npm run docker:seed:kaia
```

#### 2. Pharos Docker部署
```bash
# 部署Pharos服务（自动执行seed）
./deploy-pharos.sh

# 或者手动执行seed（如果容器已运行）
npm run docker:seed:pharos
```

## 📦 种子数据内容

### 🎯 任务数据 (Tasks)
- **文件**: `20250120073040-add_task.js`
- **内容**: 游戏任务配置数据
- **包含**: 日常任务、成就任务、社交任务等

### 🛒 IAP商品数据 (IAP Products)
- **文件**: `20250610000000-add-iap-products.js`
- **内容**: 应用内购买商品配置
- **包含**: 宝石包、增益道具、VIP会员等

### 🚜 农场配置数据 (Farm Configs)
- **脚本**: `scripts/init-farm-configs-final.sh`
- **内容**: 农场升级配置数据 (0-50级)
- **包含**: 产量、奶牛数量、速度、升级费用、离线收益等

### 🚚 配送线配置数据 (Delivery Line Configs)
- **脚本**: `scripts/init-farm-configs-final.sh`
- **内容**: 配送线升级配置数据 (1-50级)
- **包含**: 利润、容量、生产间隔、配送速度、升级费用等

### 🎯 任务配置数据 (Task Configs)
- **脚本**: `scripts/init-task-configs.sh`
- **内容**: 游戏任务配置数据 (101条任务)
- **包含**: 解锁区域、升级区域、升级流水线、邀请好友等任务
- **任务类型**:
  - **类型1**: 解锁指定区域 (ID 1-19)
  - **类型2**: 升级指定牧场区域至XX级 (ID 20-79)
  - **类型3**: 升级流水线至XX级 (ID 63-75)
  - **类型4**: 邀请好友 (ID 80-101)

### 🔄 数据库迁移
每个seed命令都会先执行数据库迁移：
```bash
npx sequelize-cli db:migrate
```

## 🗄️ 数据库配置

### 环境配置文件映射

| 命令类型 | 环境文件 | 数据库 | 说明 |
|----------|----------|--------|------|
| `local:kaia` | `.env.local.kaia` | `wolf_kaia` | Kaia本地开发数据库 |
| `local:pharos` | `.env.local.pharos` | `wolf_pharos` | Pharos本地开发数据库 |
| `docker:kaia` | `.env_kaia` | `wolf_kaia` | Kaia Docker数据库 |
| `docker:pharos` | `.env_pharos` | `wolf_pharos` | Pharos Docker数据库 |

### 数据库连接配置示例

#### 本地开发配置 (`.env.local.kaia`)
```bash
DB_NAME=wolf_kaia
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=127.0.0.1
DB_PORT=3669
```

#### Docker配置 (`.env_kaia`)
```bash
DB_NAME=wolf_kaia
DB_USER=wolf
DB_PASS=00321zixunadmin
DB_HOST=mysql
DB_PORT=3306
```

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库服务状态
docker ps | grep mysql

# 检查数据库连接
mysql -h 127.0.0.1 -P 3669 -u wolf -p00321zixunadmin -e "SHOW DATABASES;"
```

#### 2. 流水线配置迁移失败
```bash
# 错误信息：没有找到流水线配置，请先运行配置创建迁移
# 解决方案：运行配置修复脚本
npm run fix:delivery-configs

# 或者手动插入配置数据
./scripts/insert-delivery-configs.sh
```

#### 3. 种子数据重复执行
```bash
# 清理种子数据表
docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -e "DELETE FROM SequelizeData;"

# 重新执行种子数据
npm run local:seed:kaia
```

#### 4. 环境配置错误
```bash
# 检查环境配置文件是否存在
ls -la .env.local.*
ls -la .env_*

# 验证环境配置加载
ENV_FILE=.env.local.kaia node -e "require('./src/config/env.js'); console.log('DB_NAME:', process.env.DB_NAME);"
```

#### 5. 迁移执行顺序问题
如果遇到迁移依赖问题，可以按以下顺序手动执行：
```bash
# 1. 先运行数据库迁移
npm run sync:db:local:kaia

# 2. 检查流水线配置表
docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -e "SELECT COUNT(*) FROM delivery_line_configs;"

# 3. 如果配置表为空，运行修复脚本
npm run fix:delivery-configs

# 4. 再运行种子数据
npm run local:seed:kaia
```

### 日志查看

#### 本地开发环境
```bash
# 种子数据执行日志直接显示在终端
npm run local:seed:kaia
```

#### Docker环境
```bash
# 查看容器日志
docker logs wolf-fun-container
docker logs moofun-pharos-container

# 实时查看日志
docker logs -f wolf-fun-container
```

## 🔄 数据重置

### 完全重置数据库
```bash
# 本地环境重置
npm run sync:db:force:local:kaia
npm run local:seed:kaia

npm run sync:db:force:local:pharos
npm run local:seed:pharos
```

### 只重置种子数据
```bash
# 删除种子数据记录
mysql -h 127.0.0.1 -P 3669 -u wolf -p00321zixunadmin wolf_kaia -e "DELETE FROM SequelizeData WHERE name LIKE '%add_task%' OR name LIKE '%add-iap-products%';"

# 重新执行种子数据
npm run local:seed:kaia
```

## 📊 自动化集成

### 部署脚本集成
种子数据初始化已集成到部署脚本中：

#### `deploy-kaia.sh`
```bash
# 自动执行Kaia Docker种子数据
docker exec wolf-fun-container npm run seed:tasks:docker:kaia
```

#### `deploy-pharos.sh`
```bash
# 自动执行Pharos Docker种子数据
docker exec moofun-pharos-container npm run seed:tasks:docker:pharos
```

### 本地开发集成
```bash
# local-dev.sh setup 命令包含数据库同步和种子数据初始化
npm run local:setup
```

## 📚 相关文档

- [启动指南](STARTUP-GUIDE.md) - 完整的项目启动说明
- [环境配置管理](README-env-config.md) - 环境配置系统详解
- [部署指南](README-deployment.md) - 生产环境部署说明

## 💡 最佳实践

### 1. 开发流程
```bash
# 1. 启动基础服务
npm run local:docker-up

# 2. 同步数据库结构
npm run sync:db:local:kaia

# 3. 初始化种子数据
npm run local:seed:kaia

# 4. 启动应用
npm run local:kaia
```

### 2. 生产部署
```bash
# 1. 构建和部署（自动执行seed）
./deploy-kaia.sh

# 2. 验证数据
curl http://localhost:9112/api/health
```

### 3. 数据维护
- 定期备份数据库
- 版本控制种子数据文件
- 测试种子数据的幂等性

---

**💡 提示**：种子数据命令是幂等的，可以安全地重复执行。如果遇到问题，请先检查数据库连接和环境配置。
