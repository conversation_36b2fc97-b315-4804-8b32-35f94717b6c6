#!/bin/bash

# Wolf Fun 生产环境完整部署脚本
# 自动化部署 Kaia 和 Pharos 服务

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 Wolf Fun 生产环境部署脚本${NC}"
    echo ""
    echo "用法: $0 [服务] [选项]"
    echo ""
    echo "服务:"
    echo "  kaia      只部署 Kaia API 服务"
    echo "  pharos    只部署 Pharos API 服务"
    echo "  both      部署两个服务 (默认)"
    echo "  status    查看服务状态"
    echo ""
    echo "选项:"
    echo "  --skip-build    跳过镜像构建，只重启容器"
    echo "  --force-init    强制重新初始化配置数据"
    echo "  --no-init       跳过配置和种子数据初始化"
    echo ""
    echo "示例:"
    echo "  $0                      # 部署所有服务"
    echo "  $0 kaia                 # 只部署 Kaia 服务"
    echo "  $0 both --skip-build    # 跳过构建，只重启"
    echo "  $0 status               # 查看服务状态"
}

# 检查 Docker 服务
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请先启动 Docker"
        exit 1
    fi
    log_success "Docker 服务运行正常"
}

# 检查基础服务
check_base_services() {
    log_info "检查基础服务状态..."
    
    # 检查 MySQL
    if docker ps | grep -q mysql-8.3.0-wolf-shared; then
        log_success "MySQL 服务运行正常"
    else
        log_warning "MySQL 服务未运行，正在启动基础服务..."
        npm run docker:start
    fi
    
    # 检查网络
    if docker network ls | grep -q wolf_fun; then
        log_success "Docker 网络 wolf_fun 存在"
    else
        log_warning "创建 Docker 网络..."
        docker network create wolf_fun || true
    fi
}

# 部署 Kaia 服务
deploy_kaia() {
    log_info "🔨 开始部署 Kaia 服务..."
    
    if [ "$SKIP_BUILD" != true ]; then
        log_info "构建 Kaia Docker 镜像..."
        ./deploy-kaia.sh
    else
        log_info "跳过构建，重启 Kaia 容器..."
        docker stop wolf-fun-container 2>/dev/null || true
        docker rm wolf-fun-container 2>/dev/null || true
        docker run -d -p 9112:3456 --name wolf-fun-container --network wolf_fun wolf-fun
        
        if [ "$NO_INIT" != true ]; then
            log_info "初始化 Kaia 配置和数据..."
            if [ "$FORCE_INIT" = true ]; then
                docker exec wolf-fun-container ./scripts/init-all-configs.sh kaia --force
            else
                docker exec wolf-fun-container ./scripts/init-all-configs.sh kaia
            fi
            docker exec wolf-fun-container npm run seed:tasks:docker:kaia
        fi
    fi
    
    # 等待服务启动
    log_info "等待 Kaia 服务启动..."
    sleep 5
    
    # 健康检查
    if curl -f http://localhost:9112/api/health &>/dev/null; then
        log_success "Kaia 服务部署成功！"
        log_info "访问地址: http://localhost:9112/api"
    else
        log_error "Kaia 服务健康检查失败"
        return 1
    fi
}

# 部署 Pharos 服务
deploy_pharos() {
    log_info "🔨 开始部署 Pharos 服务..."
    
    if [ "$SKIP_BUILD" != true ]; then
        log_info "构建 Pharos Docker 镜像..."
        ./deploy-pharos.sh
    else
        log_info "跳过构建，重启 Pharos 容器..."
        docker stop moofun-pharos-container 2>/dev/null || true
        docker rm moofun-pharos-container 2>/dev/null || true
        docker run -d -p 9113:3457 --name moofun-pharos-container --network wolf_fun moofun-pharos
        
        if [ "$NO_INIT" != true ]; then
            log_info "初始化 Pharos 配置和数据..."
            if [ "$FORCE_INIT" = true ]; then
                docker exec moofun-pharos-container ./scripts/init-all-configs.sh pharos --force
            else
                docker exec moofun-pharos-container ./scripts/init-all-configs.sh pharos
            fi
            docker exec moofun-pharos-container npm run seed:tasks:docker:pharos
        fi
    fi
    
    # 等待服务启动
    log_info "等待 Pharos 服务启动..."
    sleep 5
    
    # 健康检查
    if curl -f http://localhost:9113/api/health &>/dev/null; then
        log_success "Pharos 服务部署成功！"
        log_info "访问地址: http://localhost:9113/api"
    else
        log_error "Pharos 服务健康检查失败"
        return 1
    fi
}

# 查看服务状态
show_status() {
    log_info "📊 服务状态检查..."
    
    echo ""
    echo "🐳 Docker 容器状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(wolf-fun|moofun-pharos|mysql|redis)" || echo "  没有找到相关容器"
    
    echo ""
    echo "🌐 服务健康检查:"
    
    # 检查 Kaia 服务
    if curl -f http://localhost:9112/api/health &>/dev/null; then
        echo "  ✅ Kaia API: http://localhost:9112/api (正常)"
    else
        echo "  ❌ Kaia API: http://localhost:9112/api (异常)"
    fi
    
    # 检查 Pharos 服务
    if curl -f http://localhost:9113/api/health &>/dev/null; then
        echo "  ✅ Pharos API: http://localhost:9113/api (正常)"
    else
        echo "  ❌ Pharos API: http://localhost:9113/api (异常)"
    fi
    
    echo ""
    echo "📊 数据库配置状态:"
    
    # 检查 Kaia 数据库配置
    local kaia_farm=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    local kaia_delivery=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_kaia -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    echo "  🚜 Kaia 农场配置: $kaia_farm 条"
    echo "  🚚 Kaia 配送线配置: $kaia_delivery 条"
    
    # 检查 Pharos 数据库配置
    local pharos_farm=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_pharos -se "SELECT COUNT(*) FROM farm_configs;" 2>/dev/null || echo "0")
    local pharos_delivery=$(docker exec mysql-8.3.0-wolf-shared mysql -u wolf -p00321zixunadmin wolf_pharos -se "SELECT COUNT(*) FROM delivery_line_configs;" 2>/dev/null || echo "0")
    echo "  🚜 Pharos 农场配置: $pharos_farm 条"
    echo "  🚚 Pharos 配送线配置: $pharos_delivery 条"
}

# 解析命令行参数
SERVICE="both"
SKIP_BUILD=false
FORCE_INIT=false
NO_INIT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both|status)
            SERVICE="$1"
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --force-init)
            FORCE_INIT=true
            shift
            ;;
        --no-init)
            NO_INIT=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun 生产环境部署"
    
    # 检查 Docker
    check_docker
    
    # 检查基础服务
    check_base_services
    
    case $SERVICE in
        kaia)
            deploy_kaia
            ;;
        pharos)
            deploy_pharos
            ;;
        both)
            deploy_kaia
            echo ""
            deploy_pharos
            ;;
        status)
            show_status
            exit 0
            ;;
        *)
            log_error "不支持的服务: $SERVICE"
            exit 1
            ;;
    esac
    
    echo ""
    log_success "🎉 部署完成！"
    show_status
}

# 运行主函数
main
