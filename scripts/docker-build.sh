#!/bin/bash

# ===========================================
# Wolf Fun Docker 构建脚本
# 支持不同环境使用不同的 .dockerignore 文件
# ===========================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🐳 Wolf Fun Docker 构建脚本${NC}"
    echo ""
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  kaia      构建 Kaia API 镜像"
    echo "  pharos    构建 Pharos API 镜像"
    echo "  both      构建两个镜像"
    echo ""
    echo "选项:"
    echo "  --no-cache    不使用构建缓存"
    echo "  --push        构建后推送到镜像仓库"
    echo "  --tag TAG     指定镜像标签 (默认: latest)"
    echo "  --registry    指定镜像仓库地址"
    echo ""
    echo "示例:"
    echo "  $0 kaia                    # 构建 Kaia 镜像"
    echo "  $0 pharos --no-cache       # 无缓存构建 Pharos 镜像"
    echo "  $0 both --tag v1.0.0       # 构建两个镜像并打标签"
    echo "  $0 kaia --push             # 构建并推送 Kaia 镜像"
}

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行"
        exit 1
    fi
}

# 备份当前的 .dockerignore
backup_dockerignore() {
    if [ -f .dockerignore ]; then
        cp .dockerignore .dockerignore.backup
        log_info "已备份当前的 .dockerignore 文件"
    fi
}

# 恢复 .dockerignore
restore_dockerignore() {
    if [ -f .dockerignore.backup ]; then
        mv .dockerignore.backup .dockerignore
        log_info "已恢复原始的 .dockerignore 文件"
    fi
}

# 使用指定环境的 .dockerignore
use_env_dockerignore() {
    local env=$1
    local dockerignore_file=".dockerignore.${env}"
    
    if [ -f "$dockerignore_file" ]; then
        cp "$dockerignore_file" .dockerignore
        log_info "使用 ${env} 环境的 .dockerignore 文件"
    else
        log_warning "${dockerignore_file} 不存在，使用默认的 .dockerignore"
    fi
}

# 构建 Docker 镜像
build_image() {
    local env=$1
    local dockerfile="Dockerfile.${env}"
    local image_name="wolf-${env}"
    local build_args=""
    
    # 检查 Dockerfile 是否存在
    if [ ! -f "$dockerfile" ]; then
        log_error "Dockerfile ${dockerfile} 不存在"
        return 1
    fi
    
    # 构建参数
    if [ "$NO_CACHE" = true ]; then
        build_args="$build_args --no-cache"
    fi
    
    if [ -n "$REGISTRY" ]; then
        image_name="${REGISTRY}/${image_name}"
    fi
    
    if [ -n "$TAG" ]; then
        image_name="${image_name}:${TAG}"
    else
        image_name="${image_name}:latest"
    fi
    
    log_info "开始构建 ${env} 镜像: ${image_name}"
    
    # 使用环境特定的 .dockerignore
    use_env_dockerignore "$env"
    
    # 构建镜像
    if docker build -f "$dockerfile" -t "$image_name" $build_args .; then
        log_success "${env} 镜像构建成功: ${image_name}"
        
        # 推送镜像
        if [ "$PUSH" = true ]; then
            log_info "推送镜像到仓库..."
            if docker push "$image_name"; then
                log_success "镜像推送成功: ${image_name}"
            else
                log_error "镜像推送失败"
                return 1
            fi
        fi
        
        return 0
    else
        log_error "${env} 镜像构建失败"
        return 1
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    restore_dockerignore
}

# 设置清理陷阱
trap cleanup EXIT

# 解析命令行参数
ENVIRONMENT=""
NO_CACHE=false
PUSH=false
TAG=""
REGISTRY=""

while [[ $# -gt 0 ]]; do
    case $1 in
        kaia|pharos|both)
            ENVIRONMENT="$1"
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --push)
            PUSH=true
            shift
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        --registry)
            REGISTRY="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$ENVIRONMENT" ]; then
    log_error "请指定构建环境 (kaia, pharos, both)"
    show_help
    exit 1
fi

# 主函数
main() {
    log_info "🚀 开始 Wolf Fun Docker 构建过程"
    
    # 检查 Docker
    check_docker
    
    # 备份当前 .dockerignore
    backup_dockerignore
    
    # 构建镜像
    case $ENVIRONMENT in
        kaia)
            build_image "kaia"
            ;;
        pharos)
            build_image "pharos"
            ;;
        both)
            log_info "构建两个环境的镜像"
            if build_image "kaia" && build_image "pharos"; then
                log_success "所有镜像构建完成"
            else
                log_error "部分镜像构建失败"
                exit 1
            fi
            ;;
        *)
            log_error "不支持的环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    log_success "🎉 Docker 构建过程完成"
}

# 运行主函数
main
