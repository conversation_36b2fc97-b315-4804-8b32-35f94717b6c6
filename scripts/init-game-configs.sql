-- 游戏配置初始化 SQL 脚本
-- 同时初始化 farm_configs 和 delivery_line_configs 表的数据

-- 使用 Kaia 数据库
USE wolf_kaia;

-- 检查当前配置数量
SELECT 'Current farm_configs count:' as info, COUNT(*) as count FROM farm_configs;
SELECT 'Current delivery_line_configs count:' as info, COUNT(*) as count FROM delivery_line_configs;

-- 清空现有数据（如果需要重新初始化）
-- DELETE FROM farm_configs;
-- DELETE FROM delivery_line_configs;

-- 插入 farm_configs 数据 (0-50级)
INSERT IGNORE INTO farm_configs (grade, production, cow, speed, milk, cost, offline, createdAt, updatedAt) VALUES
(0, 0, 0, 0, 0, 13096, 0, NOW(), NOW()),
(1, 182, 1, 100, 61, 20043, 91, NOW(), NOW()),
(2, 232, 1, 100, 77, 28583, 116, NOW(), NOW()),
(3, 276, 2, 110, 95, 39214, 138, NOW(), NOW()),
(4, 315, 2, 110, 109, 52496, 158, NOW(), NOW()),
(5, 352, 3, 120, 126, 69100, 176, NOW(), NOW()),
(6, 386, 3, 120, 138, 89837, 193, NOW(), NOW()),
(7, 418, 4, 130, 155, 115699, 209, NOW(), NOW()),
(8, 448, 4, 130, 166, 147898, 224, NOW(), NOW()),
(9, 478, 5, 140, 184, 187923, 239, NOW(), NOW()),
(10, 506, 5, 140, 195, 237594, 253, NOW(), NOW()),
(11, 533, 6, 150, 213, 299139, 266, NOW(), NOW()),
(12, 559, 6, 150, 224, 375289, 280, NOW(), NOW()),
(13, 585, 7, 160, 244, 469380, 292, NOW(), NOW()),
(14, 609, 7, 160, 254, 585495, 305, NOW(), NOW()),
(15, 633, 8, 170, 275, 728621, 317, NOW(), NOW()),
(16, 657, 8, 170, 286, 904851, 328, NOW(), NOW()),
(17, 680, 9, 180, 309, 1121623, 340, NOW(), NOW()),
(18, 702, 9, 180, 319, 1388015, 351, NOW(), NOW()),
(19, 724, 10, 190, 345, 1715098, 362, NOW(), NOW()),
(20, 746, 10, 190, 355, 2116373, 373, NOW(), NOW()),
(21, 767, 11, 200, 383, 3568859, 383, NOW(), NOW()),
(22, 1077, 11, 200, 539, 4412137, 539, NOW(), NOW()),
(23, 1110, 12, 200, 555, 5448042, 555, NOW(), NOW()),
(24, 1142, 12, 200, 571, 6719624, 571, NOW(), NOW()),
(25, 1174, 13, 200, 587, 8279413, 587, NOW(), NOW()),
(26, 1205, 13, 200, 603, 10191468, 603, NOW(), NOW()),
(27, 1236, 14, 200, 618, 12533893, 618, NOW(), NOW()),
(28, 1267, 14, 200, 634, 15401872, 634, NOW(), NOW()),
(29, 1298, 15, 210, 683, 18911373, 649, NOW(), NOW()),
(30, 1328, 15, 210, 699, 23203639, 664, NOW(), NOW()),
(31, 1358, 16, 220, 754, 28450646, 679, NOW(), NOW()),
(32, 1387, 16, 220, 771, 34861724, 694, NOW(), NOW()),
(33, 1416, 17, 230, 833, 42691606, 708, NOW(), NOW()),
(34, 1446, 17, 230, 850, 52250188, 723, NOW(), NOW()),
(35, 1474, 18, 240, 921, 63914377, 737, NOW(), NOW()),
(36, 1503, 18, 240, 939, 78142467, 751, NOW(), NOW()),
(37, 1531, 19, 250, 1021, 95491578, 766, NOW(), NOW()),
(38, 1559, 19, 250, 1040, 116638812, 780, NOW(), NOW()),
(39, 1587, 20, 260, 1134, 142406902, 794, NOW(), NOW()),
(40, 1615, 20, 260, 1153, 173795324, 807, NOW(), NOW()),
(41, 1642, 20, 270, 1263, 308830081, 821, NOW(), NOW()),
(42, 2432, 20, 270, 1871, 377475021, 1216, NOW(), NOW()),
(43, 2477, 20, 280, 2064, 461187294, 1239, NOW(), NOW()),
(44, 2522, 20, 280, 2102, 563241743, 1261, NOW(), NOW()),
(45, 2567, 20, 290, 2333, 687619368, 1283, NOW(), NOW()),
(46, 2611, 20, 290, 2374, 839158602, 1306, NOW(), NOW()),
(47, 2656, 20, 290, 2414, 1023738817, 1328, NOW(), NOW()),
(48, 2700, 20, 290, 2454, 1248502902, 1350, NOW(), NOW()),
(49, 2744, 20, 300, 2744, 1522127175, 1372, NOW(), NOW()),
(50, 2788, 20, 300, 2788, 0, 1394, NOW(), NOW());

-- 插入 delivery_line_configs 数据 (1-50级)
INSERT IGNORE INTO delivery_line_configs (grade, profit, capacity, production_interval, delivery_speed_display, upgrade_cost, created_at, updated_at) VALUES
(1, 364, 364, 2.0, 100, 13096, NOW(), NOW()),
(2, 464, 464, 2.0, 100, 20043, NOW(), NOW()),
(3, 1048, 1048, 1.9, 110, 28583, NOW(), NOW()),
(4, 1198, 1198, 1.9, 110, 39214, NOW(), NOW()),
(5, 1899, 1899, 1.8, 120, 52496, NOW(), NOW()),
(6, 2083, 2083, 1.8, 120, 69100, NOW(), NOW()),
(7, 2841, 2841, 1.7, 130, 89837, NOW(), NOW()),
(8, 3050, 3050, 1.7, 130, 115699, NOW(), NOW()),
(9, 3822, 3822, 1.6, 140, 147898, NOW(), NOW()),
(10, 4047, 4047, 1.6, 140, 282663, NOW(), NOW()),
(11, 4264, 4264, 1.6, 140, 372264, NOW(), NOW()),
(12, 4473, 4473, 1.6, 140, 488223, NOW(), NOW()),
(13, 4911, 4911, 1.4, 160, 638028, NOW(), NOW()),
(14, 5118, 5118, 1.4, 160, 831242, NOW(), NOW()),
(15, 5320, 5320, 1.2, 180, 1080077, NOW(), NOW()),
(16, 5517, 5517, 1.2, 180, 1400110, NOW(), NOW()),
(17, 5982, 5982, 1.1, 190, 1811199, NOW(), NOW()),
(18, 6179, 6179, 1.1, 190, 2338648, NOW(), NOW()),
(19, 6517, 6517, 1.0, 200, 3014677, NOW(), NOW()),
(20, 6711, 6711, 1.0, 200, 3880291, NOW(), NOW()),
(21, 6900, 6900, 0.9, 210, 4987655, NOW(), NOW()),
(22, 7542, 7542, 0.7, 230, 8761173, NOW(), NOW()),
(23, 7770, 7770, 0.7, 230, 11282638, NOW(), NOW()),
(24, 7995, 7995, 0.7, 230, 14512118, NOW(), NOW()),
(25, 8218, 8218, 0.7, 230, 18645076, NOW(), NOW()),
(26, 8438, 8438, 0.7, 230, 23930263, NOW(), NOW()),
(27, 8655, 8655, 0.7, 230, 30684106, NOW(), NOW()),
(28, 8871, 8871, 0.7, 230, 39308951, NOW(), NOW()),
(29, 9992, 9992, 0.7, 230, 50316190, NOW(), NOW()),
(30, 10224, 10224, 0.7, 230, 64355560, NOW(), NOW()),
(31, 11404, 11404, 0.7, 230, 82252268, NOW(), NOW()),
(32, 11653, 11653, 0.7, 230, 105054019, NOW(), NOW()),
(33, 12890, 12890, 0.7, 230, 134090549, NOW(), NOW()),
(34, 13154, 13154, 0.7, 230, 171049012, NOW(), NOW()),
(35, 13416, 13416, 0.7, 230, 218069387, NOW(), NOW()),
(36, 13676, 13676, 0.7, 230, 277865208, NOW(), NOW()),
(37, 15006, 15006, 0.7, 230, 353876314, NOW(), NOW()),
(38, 15281, 15281, 0.7, 230, 450462057, NOW(), NOW()),
(39, 16665, 16665, 0.7, 230, 573145646, NOW(), NOW()),
(40, 16956, 16956, 0.7, 230, 728923105, NOW(), NOW()),
(41, 18394, 18394, 0.7, 230, 926653847, NOW(), NOW()),
(42, 19456, 19456, 0.5, 250, 1715251498, NOW(), NOW()),
(43, 21055, 21055, 0.5, 250, 2183862028, NOW(), NOW()),
(44, 21437, 21437, 0.5, 250, 2779348972, NOW(), NOW()),
(45, 23101, 23101, 0.5, 250, 3535813480, NOW(), NOW()),
(46, 23502, 23502, 0.5, 250, 4496466697, NOW(), NOW()),
(47, 25229, 25229, 0.5, 250, 8312680849, NOW(), NOW()),
(48, 25648, 25648, 0.5, 250, 10648183485, NOW(), NOW()),
(49, 27438, 27438, 0.5, 250, 13635316108, NOW(), NOW()),
(50, 27876, 27876, 0.5, 250, 17454840843, NOW(), NOW());

-- 验证插入结果
SELECT 'Final farm_configs count:' as info, COUNT(*) as count FROM farm_configs;
SELECT 'Final delivery_line_configs count:' as info, COUNT(*) as count FROM delivery_line_configs;

-- 显示前5条配置样本
SELECT 'Farm configs sample:' as info;
SELECT grade, production, cow, speed, milk, cost, offline FROM farm_configs ORDER BY grade LIMIT 5;

SELECT 'Delivery configs sample:' as info;
SELECT grade, profit, capacity, production_interval, upgrade_cost FROM delivery_line_configs ORDER BY grade LIMIT 5;
