# ===========================================
# Wolf Fun 项目通用 Docker 忽略文件
# ===========================================

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 构建输出
dist/
build/
*.tgz
*.tar.gz

# 日志文件
logs/
*.log
pids/
*.pid
*.seed
*.pid.lock

# 运行时数据
lib-cov/
coverage/
.nyc_output/
.grunt/
.lock-wscript/

# 依赖目录
jspm_packages/
bower_components/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm/

# 可选的 eslint 缓存
.eslintcache

# 微服务缓存目录
.microbundle-cache/

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# dotenv 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 本地开发环境配置
.env.local.*
.env.api1
.env.api2

# parcel-bundler 缓存 (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js 构建输出
.next/

# Nuxt.js 构建 / 生成输出
.nuxt/
dist/

# Gatsby 文件
.cache/
public/

# Vuepress 构建输出
.vuepress/dist/

# Serverless 目录
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# 存储在 Webpack 中的 Stores
.stores/

# ===========================================
# 开发工具和 IDE
# ===========================================

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===========================================
# 操作系统文件
# ===========================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===========================================
# 版本控制
# ===========================================

.git/
.gitignore
.gitattributes
.gitmodules

# SVN
.svn/

# Mercurial
.hg/
.hgignore
.hgtags

# ===========================================
# 数据库和存储
# ===========================================

# MySQL 数据文件
mysql-data/
*.sql
*.db
*.sqlite
*.sqlite3

# Redis 数据文件
dump.rdb
appendonly.aof

# MongoDB 数据文件
data/

# ===========================================
# Docker 相关
# ===========================================

# Docker 文件（保留需要的）
!Dockerfile*
!docker-compose*.yml
.dockerignore

# Docker 构建缓存
.docker/

# ===========================================
# 测试和覆盖率
# ===========================================

# Jest
coverage/
.nyc_output/

# Mocha
.mocha.opts

# 测试结果
test-results/
junit.xml

# ===========================================
# 文档和说明
# ===========================================

# Markdown 文件（根据需要调整）
README*.md
CHANGELOG.md
CONTRIBUTING.md
LICENSE
*.md

# 文档目录
docs/
doc/

# ===========================================
# 脚本和工具
# ===========================================

# 开发脚本
scripts/
*.sh
*.bat
*.cmd

# 部署脚本
deploy*.sh
start*.sh
stop*.sh

# ===========================================
# 配置文件
# ===========================================

# PM2 配置
ecosystem.config.js

# ESLint 配置
.eslintrc*
.eslintignore

# Prettier 配置
.prettierrc*
.prettierignore

# TypeScript 配置
tsconfig*.json

# Babel 配置
.babelrc*
babel.config.*

# Webpack 配置
webpack.config.*

# Rollup 配置
rollup.config.*

# ===========================================
# 临时文件和缓存
# ===========================================

# 临时文件
tmp/
temp/
.tmp/

# 缓存文件
.cache/
*.cache

# 备份文件
*.bak
*.backup
*.old

# ===========================================
# 安全和密钥
# ===========================================

# 私钥和证书
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# 密钥文件
secrets/
.secrets/

# ===========================================
# 其他
# ===========================================

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz

# 图片文件（如果不需要）
# *.jpg
# *.jpeg
# *.png
# *.gif
# *.svg

# 字体文件（如果不需要）
# *.ttf
# *.woff
# *.woff2
# *.eot
