-- 初始化多个数据库的脚本
-- 这个脚本会在MySQL容器首次启动时自动执行

-- 创建 Kaia API 数据库
CREATE DATABASE IF NOT EXISTS wolf_kaia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建 Pharos API 数据库
CREATE DATABASE IF NOT EXISTS wolf_pharos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 保持原有的wolf数据库（兼容性）
CREATE DATABASE IF NOT EXISTS wolf CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 为了兼容现有配置，给通用 wolf 用户授权访问所有数据库
GRANT ALL PRIVILEGES ON wolf_kaia.* TO 'wolf'@'%';
GRANT ALL PRIVILEGES ON wolf_pharos.* TO 'wolf'@'%';
GRANT ALL PRIVILEGES ON wolf.* TO 'wolf'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示创建的数据库
SELECT 'Databases created successfully:' as Status;
SHOW DATABASES LIKE 'wolf%';
